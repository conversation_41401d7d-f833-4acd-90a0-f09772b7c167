% !TeX TS-program = xelatex

\documentclass{resume}
\ResumeName{朱祉睿}

% 如果想插入照片，请使用以下两个库。
% \usepackage{graphicx}
% \usepackage{tikz}

% 压缩版面设置
\usepackage{setspace}
\setstretch{0.9}  % 进一步减少行距
\setlength{\parskip}{0.1em}  % 进一步减少段落间距
\setlength{\itemsep}{0pt}  % 减少列表项间距
\setlength{\parsep}{0pt}   % 减少段落解析间距

\begin{document}

\ResumeContacts{
  电话：18229395676,%
  求职意向：算法工程师,%
  邮箱：\ResumeUrl{mailto:<EMAIL>}{<EMAIL>},%
  毕业时间：2026年6月%
}

% 如果想插入照片，请取消此代码的注释。
% 但是默认不推荐插入照片，因为这不是简历的重点。
% 如果默认的照片插入格式不能满足你的需求，你可以尝试调整照片的大小，或者使用其他的插入照片的方法。
% 不然，也可以先渲染 PDF 简历，然后用其他工具在 PDF 上叠加照片。
% \begin{tikzpicture}[remember picture, overlay]
%   \node [anchor=north east, inner sep=1cm]  at (current page.north east) 
%      {\includegraphics[width=2cm]{image.png}};
% \end{tikzpicture}

\ResumeTitle

\section{教育背景}
\ResumeItem
[中山大学|硕士研究生]
{\ResumeUrl{https://www.sysu.edu.cn}{\heiti 中山大学}}
[\textnormal{应用统计（数学学院）|} 硕士（保研）]
[2024.09—2026.06]


\textbf{奖学金：} 中山大学研究生奖学金一等奖（研一）

\vspace{0.5em}
\ResumeItem
[南方科技大学|本科生]
{\ResumeUrl{https://www.sustech.edu.cn}{\heiti 南方科技大学}}
[\textnormal{统计学（统计与数据科学学院）|} 本科]
[2020.09—2024.06]

\section{实习经历}

\ResumeItem{{\Large\ResumeUrl{https://www.kuaishou.com}{\heiti 快手}}}
[商业化\hfill\phantom{商业化}]
[\hfill 广告算法实习 \hfill]
[2025.04—至今]

% \textbf{搜索广告精排表单CVR模型消偏}
\textbf{\large 搜索广告精排表单CVR模型消偏}

\textbf{项目背景：}（1）解决因训练样本中搜索、信息流样本比例悬殊，导致原表单 CVR 模型在搜索场景下的预估偏差的问题；（2）解决 leave time 停留时间这一强相关信号仅作粗粒度二分类处理，导致时序信息有损的问题。

\textbf{技术方案：}
\begin{itemize}
  \item \textbf{精细化时长建模：} （1）引入保序回归的思想，将停留时长做非均匀分桶（2）对桶间时序依赖进行建模。采用ESMM进行信号监督，构造分桶预测概率的马尔可夫链，作为辅助信息融入主塔。
  \item \textbf{引入辅助任务：} 停留时长有大量值为0的样本出现，因此单独建模一个网络预测leave time是否大于0。
  \item \textbf{设计Bias网络：} 针对搜索样本稀疏问题，对信息流样本进行负样本下采样。设计 Bias 网络，将 Search 特征通过 POSO Gate-Layer 纠偏网络，融入主干网络。
\end{itemize}

\textbf{成果：}离线AUC +0.5\%。搜索表单消耗\textbf{+0.897\%}，客户预期花费\textbf{+4.082\%}，转换数\textbf{+3.201\%}。

\vspace{0.5em}

\textbf{\large 基于残差建模的直播实时发券模型}

\textbf{项目背景：}在 QCPX 广告拍卖机制下，将部分广告预算（或平台出资）转为优惠券发放给价格敏感用户以提升曝光转化率，同时提升平台变现效率和客户跑量。

\textbf{技术方案：}
\begin{itemize}
  \item \textbf{Uplift建模:} 使用Regression spline的分段线性建模，构造 Multi-treatment 残差弹性网络，实时预估多面额发券对CVR的增量价值。
  \item \textbf{RCT实验:} 进行严格的RCT实验，以AUUC等指标评估模型因果效应排序能力(模型 1.59 vs 随机0.53)。
  \item \textbf{连续值预估:} 使用改进的 Youtube Weighted LR 损失函数,引入假负样本,实现对搜索直播大卡的增量订单数的无偏预估。
  \item \textbf{策略优化:} 在QCPX场景下，构建带mROI约束的优化问题，进而通过调节超参数影响最优面额求解，实现对mROI的精准调控，实现eCPM最大化。
\end{itemize}

\textbf{成果：}PCOC从0.87到0.96，AUC提升约3.6点，直播整体消耗\textbf{+0.302\%}，预期花费\textbf{+0.302\%}，转换数\textbf{+1.667\%}

\vspace{1em}

\ResumeItem{{\Large\ResumeUrl{https://www.bytedance.com}{\heiti 字节跳动}}}
[业务中台\hfill\phantom{业务中台}]
[\hfill 大模型算法实习 \hfill]
[2024.12—2024.4]

\textbf{\large 基于 LLM 的 AI LQA功能效果调优}

\textbf{项目背景：}字节跳动 STARLING 平台的 AI LQA 功能旨在自动进行翻译质检。因准确率低 (基线合格率<65\%) 且误报严重，阻碍了 TikTok、PDI 等多业务线的有效应用。

\textbf{技术方案：}
\begin{itemize}
  \item \textbf{数据工程与增强：} （1）利用平台的LQA 数据，借助 GPT-4o、Deepseek-R1 等模型及人工反馈，构造COT数据，为训练样本生成精细化的错误分析论证过程。（2）应用 Pairwise 数据构建策略，将同一原文的参考翻译与错误翻译配对输入，强化模型对正负样本的对比学习能力。针对复杂错误类型如词汇遗漏、词汇不当等与数据不平衡问题，采用投票机制筛选。

  \item \textbf{模型训练与优化：} 对 Qwen2.5-7B 进行基于 LoRA 的 SFT ，优化模型输出包含判错结果及结构化推理过程的响应。
\end{itemize}

\textbf{成果：}AI LQA 模型的判错二分类Recall与Precision均稳定提升至 90\% 以上，远超基线水平 (<64\%)。

\vspace{0.5em}

\textbf{\large 基于 LLM 的 AI翻译调优}

\textbf{项目背景：}提升特定业务场景（如 TT 话题/文档、PDI 中英翻译）的机器翻译质量。

\textbf{技术方案：}
\begin{itemize}
  \item \textbf{Reward奖励函数设计：} 采用 GRPO,DPO 对 Qwen2.5-7B 进行 Post-Training，设计结合 BLEU、TER 等自动化指标的句级 Reward 函数，增强其对模型优化方向的引导能力。
  \item \textbf{Agent搭建：} 构建LLM多阶段自主优化翻译流程，引入术语表以及风格指南，融合多维规范的Agent反思与迭代修正核心，引导及规范化译文。
\end{itemize}

\textbf{成果：}人工 LQA 分数稳定提升至 80\% 以上 ，超过基线水平 (<74\%)。


\section{工作技能}
\begin{itemize}
\item \textbf{核心优势：} 掌握推荐系统、广告算法、大模型等领域的前沿理论与实践经验。
\item \textbf{工具框架：}Tensorflow, Pytorch, 熟悉模型构建，训练与部署
\item \textbf{语言能力：}英语（CET-6）
\end{itemize}
\end{document}
