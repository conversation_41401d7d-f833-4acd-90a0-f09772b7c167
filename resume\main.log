This is XeTeX, Version 3.141592653-2.6-0.999997 (MiKTeX 25.4) (preloaded format=xelatex 2025.7.27)  7 AUG 2025 00:36
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**c:/Users/<USER>/Desktop/resume/resume/main.tex
(c:/Users/<USER>/Desktop/resume/resume/main.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-19>
(resume.cls (E:\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2025-07-19 L3 programming layer (loader) 
 (E:\MiKTeX\tex/latex/l3backend\l3backend-xetex.def
File: l3backend-xetex.def 2025-06-09 L3 backend support: XeTeX
\g__graphics_track_int=\count271
\g__pdfannot_backend_int=\count272
\g__pdfannot_backend_link_int=\count273
)) (E:\MiKTeX\tex/latex/l3packages/l3keys2e\l3keys2e.sty
Package: l3keys2e 2024-08-16 LaTeX2e option processing using LaTeX3 keys
)
Document Class: resume 2022-12-26 v0.1.0 Another Resume Class by Feng Kaiyu
(E:\MiKTeX\tex/latex/ctex\ctexart.cls (E:\MiKTeX\tex/latex/ctex/config\ctexbackend.cfg
File: ctexbackend.cfg 2022/07/14 v2.5.10 Backend configuration file (CTEX)
)
Document Class: ctexart 2022/07/14 v2.5.10 Chinese adapter for class article (CTEX)
(E:\MiKTeX\tex/latex/ctex\ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (E:\MiKTeX\tex/latex/ctex\ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
) (E:\MiKTeX\tex/latex/base\fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (E:\MiKTeX\tex/latex/base\ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
LaTeX Encoding Info:    Redeclaring text command \capitalcedilla (encoding TS1) on input line 49.
LaTeX Encoding Info:    Redeclaring text command \capitalogonek (encoding TS1) on input line 52.
LaTeX Encoding Info:    Redeclaring text command \capitalgrave (encoding TS1) on input line 55.
LaTeX Encoding Info:    Redeclaring text command \capitalacute (encoding TS1) on input line 56.
LaTeX Encoding Info:    Redeclaring text command \capitalcircumflex (encoding TS1) on input line 57.
LaTeX Encoding Info:    Redeclaring text command \capitaltilde (encoding TS1) on input line 58.
LaTeX Encoding Info:    Redeclaring text command \capitaldieresis (encoding TS1) on input line 59.
LaTeX Encoding Info:    Redeclaring text command \capitalhungarumlaut (encoding TS1) on input line 60.
LaTeX Encoding Info:    Redeclaring text command \capitalring (encoding TS1) on input line 61.
LaTeX Encoding Info:    Redeclaring text command \capitalcaron (encoding TS1) on input line 62.
LaTeX Encoding Info:    Redeclaring text command \capitalbreve (encoding TS1) on input line 63.
LaTeX Encoding Info:    Redeclaring text command \capitalmacron (encoding TS1) on input line 64.
LaTeX Encoding Info:    Redeclaring text command \capitaldotaccent (encoding TS1) on input line 65.
LaTeX Encoding Info:    Redeclaring text command \t (encoding TS1) on input line 66.
LaTeX Encoding Info:    Redeclaring text command \capitaltie (encoding TS1) on input line 67.
LaTeX Encoding Info:    Redeclaring text command \newtie (encoding TS1) on input line 68.
LaTeX Encoding Info:    Redeclaring text command \capitalnewtie (encoding TS1) on input line 69.
LaTeX Encoding Info:    Redeclaring text symbol \textcapitalcompwordmark (encoding TS1) on input line 70.
LaTeX Encoding Info:    Redeclaring text symbol \textascendercompwordmark (encoding TS1) on input line 71.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightbase (encoding TS1) on input line 72.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightdblbase (encoding TS1) on input line 73.
LaTeX Encoding Info:    Redeclaring text symbol \texttwelveudash (encoding TS1) on input line 74.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequartersemdash (encoding TS1) on input line 75.
LaTeX Encoding Info:    Redeclaring text symbol \textleftarrow (encoding TS1) on input line 76.
LaTeX Encoding Info:    Redeclaring text symbol \textrightarrow (encoding TS1) on input line 77.
LaTeX Encoding Info:    Redeclaring text symbol \textblank (encoding TS1) on input line 78.
LaTeX Encoding Info:    Redeclaring text symbol \textdollar (encoding TS1) on input line 79.
LaTeX Encoding Info:    Redeclaring text symbol \textquotesingle (encoding TS1) on input line 80.
LaTeX Encoding Info:    Redeclaring text command \textasteriskcentered (encoding TS1) on input line 81.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphen (encoding TS1) on input line 92.
LaTeX Encoding Info:    Redeclaring text symbol \textfractionsolidus (encoding TS1) on input line 93.
LaTeX Encoding Info:    Redeclaring text symbol \textzerooldstyle (encoding TS1) on input line 94.
LaTeX Encoding Info:    Redeclaring text symbol \textoneoldstyle (encoding TS1) on input line 95.
LaTeX Encoding Info:    Redeclaring text symbol \texttwooldstyle (encoding TS1) on input line 96.
LaTeX Encoding Info:    Redeclaring text symbol \textthreeoldstyle (encoding TS1) on input line 97.
LaTeX Encoding Info:    Redeclaring text symbol \textfouroldstyle (encoding TS1) on input line 98.
LaTeX Encoding Info:    Redeclaring text symbol \textfiveoldstyle (encoding TS1) on input line 99.
LaTeX Encoding Info:    Redeclaring text symbol \textsixoldstyle (encoding TS1) on input line 100.
LaTeX Encoding Info:    Redeclaring text symbol \textsevenoldstyle (encoding TS1) on input line 101.
LaTeX Encoding Info:    Redeclaring text symbol \texteightoldstyle (encoding TS1) on input line 102.
LaTeX Encoding Info:    Redeclaring text symbol \textnineoldstyle (encoding TS1) on input line 103.
LaTeX Encoding Info:    Redeclaring text symbol \textlangle (encoding TS1) on input line 104.
LaTeX Encoding Info:    Redeclaring text symbol \textminus (encoding TS1) on input line 105.
LaTeX Encoding Info:    Redeclaring text symbol \textrangle (encoding TS1) on input line 106.
LaTeX Encoding Info:    Redeclaring text symbol \textmho (encoding TS1) on input line 107.
LaTeX Encoding Info:    Redeclaring text symbol \textbigcircle (encoding TS1) on input line 108.
LaTeX Encoding Info:    Redeclaring text command \textcircled (encoding TS1) on input line 109.
LaTeX Encoding Info:    Redeclaring text symbol \textohm (encoding TS1) on input line 115.
LaTeX Encoding Info:    Redeclaring text symbol \textlbrackdbl (encoding TS1) on input line 116.
LaTeX Encoding Info:    Redeclaring text symbol \textrbrackdbl (encoding TS1) on input line 117.
LaTeX Encoding Info:    Redeclaring text symbol \textuparrow (encoding TS1) on input line 118.
LaTeX Encoding Info:    Redeclaring text symbol \textdownarrow (encoding TS1) on input line 119.
LaTeX Encoding Info:    Redeclaring text symbol \textasciigrave (encoding TS1) on input line 120.
LaTeX Encoding Info:    Redeclaring text symbol \textborn (encoding TS1) on input line 121.
LaTeX Encoding Info:    Redeclaring text symbol \textdivorced (encoding TS1) on input line 122.
LaTeX Encoding Info:    Redeclaring text symbol \textdied (encoding TS1) on input line 123.
LaTeX Encoding Info:    Redeclaring text symbol \textleaf (encoding TS1) on input line 124.
LaTeX Encoding Info:    Redeclaring text symbol \textmarried (encoding TS1) on input line 125.
LaTeX Encoding Info:    Redeclaring text symbol \textmusicalnote (encoding TS1) on input line 126.
LaTeX Encoding Info:    Redeclaring text symbol \texttildelow (encoding TS1) on input line 127.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphenchar (encoding TS1) on input line 128.
LaTeX Encoding Info:    Redeclaring text symbol \textasciibreve (encoding TS1) on input line 129.
LaTeX Encoding Info:    Redeclaring text symbol \textasciicaron (encoding TS1) on input line 130.
LaTeX Encoding Info:    Redeclaring text symbol \textacutedbl (encoding TS1) on input line 131.
LaTeX Encoding Info:    Redeclaring text symbol \textgravedbl (encoding TS1) on input line 132.
LaTeX Encoding Info:    Redeclaring text symbol \textdagger (encoding TS1) on input line 133.
LaTeX Encoding Info:    Redeclaring text symbol \textdaggerdbl (encoding TS1) on input line 134.
LaTeX Encoding Info:    Redeclaring text symbol \textbardbl (encoding TS1) on input line 135.
LaTeX Encoding Info:    Redeclaring text symbol \textperthousand (encoding TS1) on input line 136.
LaTeX Encoding Info:    Redeclaring text symbol \textbullet (encoding TS1) on input line 137.
LaTeX Encoding Info:    Redeclaring text symbol \textcelsius (encoding TS1) on input line 138.
LaTeX Encoding Info:    Redeclaring text symbol \textdollaroldstyle (encoding TS1) on input line 139.
LaTeX Encoding Info:    Redeclaring text symbol \textcentoldstyle (encoding TS1) on input line 140.
LaTeX Encoding Info:    Redeclaring text symbol \textflorin (encoding TS1) on input line 141.
LaTeX Encoding Info:    Redeclaring text symbol \textcolonmonetary (encoding TS1) on input line 142.
LaTeX Encoding Info:    Redeclaring text symbol \textwon (encoding TS1) on input line 143.
LaTeX Encoding Info:    Redeclaring text symbol \textnaira (encoding TS1) on input line 144.
LaTeX Encoding Info:    Redeclaring text symbol \textguarani (encoding TS1) on input line 145.
LaTeX Encoding Info:    Redeclaring text symbol \textpeso (encoding TS1) on input line 146.
LaTeX Encoding Info:    Redeclaring text symbol \textlira (encoding TS1) on input line 147.
LaTeX Encoding Info:    Redeclaring text symbol \textrecipe (encoding TS1) on input line 148.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobang (encoding TS1) on input line 149.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobangdown (encoding TS1) on input line 150.
LaTeX Encoding Info:    Redeclaring text symbol \textdong (encoding TS1) on input line 151.
LaTeX Encoding Info:    Redeclaring text symbol \texttrademark (encoding TS1) on input line 152.
LaTeX Encoding Info:    Redeclaring text symbol \textpertenthousand (encoding TS1) on input line 153.
LaTeX Encoding Info:    Redeclaring text symbol \textpilcrow (encoding TS1) on input line 154.
LaTeX Encoding Info:    Redeclaring text symbol \textbaht (encoding TS1) on input line 155.
LaTeX Encoding Info:    Redeclaring text symbol \textnumero (encoding TS1) on input line 156.
LaTeX Encoding Info:    Redeclaring text symbol \textdiscount (encoding TS1) on input line 157.
LaTeX Encoding Info:    Redeclaring text symbol \textestimated (encoding TS1) on input line 158.
LaTeX Encoding Info:    Redeclaring text symbol \textopenbullet (encoding TS1) on input line 159.
LaTeX Encoding Info:    Redeclaring text symbol \textservicemark (encoding TS1) on input line 160.
LaTeX Encoding Info:    Redeclaring text symbol \textlquill (encoding TS1) on input line 161.
LaTeX Encoding Info:    Redeclaring text symbol \textrquill (encoding TS1) on input line 162.
LaTeX Encoding Info:    Redeclaring text symbol \textcent (encoding TS1) on input line 163.
LaTeX Encoding Info:    Redeclaring text symbol \textsterling (encoding TS1) on input line 164.
LaTeX Encoding Info:    Redeclaring text symbol \textcurrency (encoding TS1) on input line 165.
LaTeX Encoding Info:    Redeclaring text symbol \textyen (encoding TS1) on input line 166.
LaTeX Encoding Info:    Redeclaring text symbol \textbrokenbar (encoding TS1) on input line 167.
LaTeX Encoding Info:    Redeclaring text symbol \textsection (encoding TS1) on input line 168.
LaTeX Encoding Info:    Redeclaring text symbol \textasciidieresis (encoding TS1) on input line 169.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyright (encoding TS1) on input line 170.
LaTeX Encoding Info:    Redeclaring text symbol \textordfeminine (encoding TS1) on input line 171.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyleft (encoding TS1) on input line 172.
LaTeX Encoding Info:    Redeclaring text symbol \textlnot (encoding TS1) on input line 173.
LaTeX Encoding Info:    Redeclaring text symbol \textcircledP (encoding TS1) on input line 174.
LaTeX Encoding Info:    Redeclaring text symbol \textregistered (encoding TS1) on input line 175.
LaTeX Encoding Info:    Redeclaring text symbol \textasciimacron (encoding TS1) on input line 176.
LaTeX Encoding Info:    Redeclaring text symbol \textdegree (encoding TS1) on input line 177.
LaTeX Encoding Info:    Redeclaring text symbol \textpm (encoding TS1) on input line 178.
LaTeX Encoding Info:    Redeclaring text symbol \texttwosuperior (encoding TS1) on input line 179.
LaTeX Encoding Info:    Redeclaring text symbol \textthreesuperior (encoding TS1) on input line 180.
LaTeX Encoding Info:    Redeclaring text symbol \textasciiacute (encoding TS1) on input line 181.
LaTeX Encoding Info:    Redeclaring text symbol \textmu (encoding TS1) on input line 182.
LaTeX Encoding Info:    Redeclaring text symbol \textparagraph (encoding TS1) on input line 183.
LaTeX Encoding Info:    Redeclaring text symbol \textperiodcentered (encoding TS1) on input line 184.
LaTeX Encoding Info:    Redeclaring text symbol \textreferencemark (encoding TS1) on input line 185.
LaTeX Encoding Info:    Redeclaring text symbol \textonesuperior (encoding TS1) on input line 186.
LaTeX Encoding Info:    Redeclaring text symbol \textordmasculine (encoding TS1) on input line 187.
LaTeX Encoding Info:    Redeclaring text symbol \textsurd (encoding TS1) on input line 188.
LaTeX Encoding Info:    Redeclaring text symbol \textonequarter (encoding TS1) on input line 189.
LaTeX Encoding Info:    Redeclaring text symbol \textonehalf (encoding TS1) on input line 190.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequarters (encoding TS1) on input line 191.
LaTeX Encoding Info:    Redeclaring text symbol \texteuro (encoding TS1) on input line 192.
LaTeX Encoding Info:    Redeclaring text symbol \texttimes (encoding TS1) on input line 193.
LaTeX Encoding Info:    Redeclaring text symbol \textdiv (encoding TS1) on input line 194.
))
\l__ctex_tmp_int=\count274
\l__ctex_tmp_box=\box53
\l__ctex_tmp_dim=\dimen148
\g__ctex_section_depth_int=\count275
\g__ctex_font_size_int=\count276
 (E:\MiKTeX\tex/latex/ctex/config\ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
) (E:\MiKTeX\tex/latex/base\article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(E:\MiKTeX\tex/latex/base\size10.clo
File: size10.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count277
\c@section=\count278
\c@subsection=\count279
\c@subsubsection=\count280
\c@paragraph=\count281
\c@subparagraph=\count282
\c@figure=\count283
\c@table=\count284
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen149
) (E:\MiKTeX\tex/latex/ctex/engine\ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)
 (E:\MiKTeX\tex/xelatex/xecjk\xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX
 (E:\MiKTeX\tex/latex/l3packages/xtemplate\xtemplate.sty
Package: xtemplate 2024-08-16 L3 Experimental prototype document functions
)
\l__xeCJK_tmp_int=\count285
\l__xeCJK_tmp_box=\box54
\l__xeCJK_tmp_dim=\dimen150
\l__xeCJK_tmp_skip=\skip51
\g__xeCJK_space_factor_int=\count286
\l__xeCJK_begin_int=\count287
\l__xeCJK_end_int=\count288
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip52
\c__xeCJK_none_node=\count289
\g__xeCJK_node_int=\count290
\c__xeCJK_CJK_node_dim=\dimen151
\c__xeCJK_CJK-space_node_dim=\dimen152
\c__xeCJK_default_node_dim=\dimen153
\c__xeCJK_CJK-widow_node_dim=\dimen154
\c__xeCJK_normalspace_node_dim=\dimen155
\c__xeCJK_default-space_node_skip=\skip53
\l__xeCJK_ccglue_skip=\skip54
\l__xeCJK_ecglue_skip=\skip55
\l__xeCJK_punct_kern_skip=\skip56
\l__xeCJK_indent_box=\box55
\l__xeCJK_last_penalty_int=\count291
\l__xeCJK_last_bound_dim=\dimen156
\l__xeCJK_last_kern_dim=\dimen157
\l__xeCJK_widow_penalty_int=\count292

LaTeX template Info: Declaring template type 'xeCJK/punctuation' taking 0
(template)           argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen158
\l__xeCJK_mixed_punct_width_dim=\dimen159
\l__xeCJK_middle_punct_width_dim=\dimen160
\l__xeCJK_fixed_margin_width_dim=\dimen161
\l__xeCJK_mixed_margin_width_dim=\dimen162
\l__xeCJK_middle_margin_width_dim=\dimen163
\l__xeCJK_bound_punct_width_dim=\dimen164
\l__xeCJK_bound_margin_width_dim=\dimen165
\l__xeCJK_margin_minimum_dim=\dimen166
\l__xeCJK_kerning_total_width_dim=\dimen167
\l__xeCJK_same_align_margin_dim=\dimen168
\l__xeCJK_different_align_margin_dim=\dimen169
\l__xeCJK_kerning_margin_width_dim=\dimen170
\l__xeCJK_kerning_margin_minimum_dim=\dimen171
\l__xeCJK_bound_dim=\dimen172
\l__xeCJK_reverse_bound_dim=\dimen173
\l__xeCJK_margin_dim=\dimen174
\l__xeCJK_minimum_bound_dim=\dimen175
\l__xeCJK_kerning_margin_dim=\dimen176
\g__xeCJK_family_int=\count293
\l__xeCJK_fam_int=\count294
\g__xeCJK_fam_allocation_int=\count295
\l__xeCJK_verb_case_int=\count296
\l__xeCJK_verb_exspace_skip=\skip57
 (E:\MiKTeX\tex/latex/fontspec\fontspec.sty (E:\MiKTeX\tex/latex/l3packages/xparse\xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
 (E:\MiKTeX\tex/latex/fontspec\fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count297
\l__fontspec_language_int=\count298
\l__fontspec_strnum_int=\count299
\l__fontspec_tmp_int=\count300
\l__fontspec_tmpa_int=\count301
\l__fontspec_tmpb_int=\count302
\l__fontspec_tmpc_int=\count303
\l__fontspec_em_int=\count304
\l__fontspec_emdef_int=\count305
\l__fontspec_strong_int=\count306
\l__fontspec_strongdef_int=\count307
\l__fontspec_tmpa_dim=\dimen177
\l__fontspec_tmpb_dim=\dimen178
\l__fontspec_tmpc_dim=\dimen179
 (E:\MiKTeX\tex/latex/base\fontenc.sty
Package: fontenc 2024/12/21 v2.1c Standard LaTeX package
) (E:\MiKTeX\tex/latex/fontspec\fontspec.cfg))) (E:\MiKTeX\tex/xelatex/xecjk\xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen180
\l__ctex_ccglue_skip=\skip58
)
\l__ctex_ziju_dim=\dimen181
 (E:\MiKTeX\tex/latex/zhnumber\zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count308
\l__zhnum_tmp_int=\count309
 (E:\MiKTeX\tex/latex/zhnumber\zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
))
\l__ctex_heading_skip=\skip59
 (E:\MiKTeX\tex/latex/ctex/scheme\ctex-scheme-chinese-article.def
File: ctex-scheme-chinese-article.def 2022/07/14 v2.5.10 Chinese scheme for article (CTEX)
 (E:\MiKTeX\tex/latex/ctex/config\ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
)) (E:\MiKTeX\tex/latex/ctex\ctex-c5size.clo
File: ctex-c5size.clo 2022/07/14 v2.5.10 c5size option (CTEX)
) (E:\MiKTeX\tex/latex/ctex/fontset\ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTEX)
)) (E:\MiKTeX\tex/latex/ctex/config\ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
) (E:\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (E:\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (E:\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (E:\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count310
\Gm@cntv=\count311
\c@Gm@tempcnt=\count312
\Gm@bindingoffset=\dimen182
\Gm@wd@mp=\dimen183
\Gm@odd@mp=\dimen184
\Gm@even@mp=\dimen185
\Gm@layoutwidth=\dimen186
\Gm@layoutheight=\dimen187
\Gm@layouthoffset=\dimen188
\Gm@layoutvoffset=\dimen189
\Gm@dimlist=\toks18
 (E:\MiKTeX\tex/latex/geometry\geometry.cfg)) (E:\MiKTeX\tex/latex/fancyhdr\fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers
\f@nch@headwidth=\skip60
\f@nch@offset@elh=\skip61
\f@nch@offset@erh=\skip62
\f@nch@offset@olh=\skip63
\f@nch@offset@orh=\skip64
\f@nch@offset@elf=\skip65
\f@nch@offset@erf=\skip66
\f@nch@offset@olf=\skip67
\f@nch@offset@orf=\skip68
\f@nch@height=\skip69
\f@nch@footalignment=\skip70
\f@nch@widthL=\skip71
\f@nch@widthC=\skip72
\f@nch@widthR=\skip73
\@temptokenb=\toks19
) (E:\MiKTeX\tex/latex/enumitem\enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip74
\enit@outerparindent=\dimen190
\enit@toks=\toks20
\enit@inbox=\box56
\enit@count@id=\count313
\enitdp@description=\count314
) (E:\MiKTeX\tex/latex/footmisc\footmisc.sty
Package: footmisc 2025/05/09 v7.0b a miscellany of footnote facilities
\FN@temptoken=\toks21
\footnotemargin=\dimen191
\FN@tempboxb=\box57
\FN@tempboxc=\box58
\footglue=\skip75
\footnotebaselineskip=\dimen192
Package footmisc Info: Declaring symbol style bringhurst on input line 549.
Package footmisc Info: Declaring symbol style chicago on input line 557.
Package footmisc Info: Declaring symbol style wiley on input line 566.
Package footmisc Info: Declaring symbol style lamport-robust on input line 577.
Package footmisc Info: Declaring symbol style lamport* on input line 597.
Package footmisc Info: Declaring symbol style lamport*-robust on input line 618.
) (E:\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2025-07-12 v7.01o Hypertext links for LaTeX
 (E:\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (E:\MiKTeX\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (E:\MiKTeX\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (E:\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (E:\MiKTeX\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (E:\MiKTeX\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (E:\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (E:\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2025-06-21 v2.57 Cross-referencing by name of section
 (E:\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (E:\MiKTeX\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (E:\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count315
) (E:\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count316
) (E:\MiKTeX\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen193
\Hy@linkcounter=\count317
\Hy@pagecounter=\count318
 (E:\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2025-07-12 v7.01o Hyperref: PDFDocEncoding definition (HO)
) (E:\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count319
 (E:\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2025-07-12 v7.01o Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4066.
Package hyperref Info: Option `colorlinks' set `false' on input line 4066.
Package hyperref Info: Hyper figures OFF on input line 4195.
Package hyperref Info: Link nesting OFF on input line 4200.
Package hyperref Info: Hyper index ON on input line 4203.
Package hyperref Info: Plain pages OFF on input line 4210.
Package hyperref Info: Backreferencing OFF on input line 4215.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4462.
\c@Hy@tempcnt=\count320
 (E:\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4801.
\XeTeXLinkMargin=\dimen194
 (E:\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (E:\MiKTeX\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count321
\Field@Width=\dimen195
\Fld@charsize=\dimen196
Package hyperref Info: Hyper figures OFF on input line 6078.
Package hyperref Info: Link nesting OFF on input line 6083.
Package hyperref Info: Hyper index ON on input line 6086.
Package hyperref Info: backreferencing OFF on input line 6093.
Package hyperref Info: Link coloring OFF on input line 6098.
Package hyperref Info: Link coloring with OCG OFF on input line 6103.
Package hyperref Info: PDF/A mode OFF on input line 6108.
\Hy@abspage=\count322
\c@Item=\count323
\c@Hfootnote=\count324
)
Package hyperref Info: Driver (autodetected): hxetex.
 (E:\MiKTeX\tex/latex/hyperref\hxetex.def
File: hxetex.def 2025-07-12 v7.01o Hyperref driver for XeTeX
\pdfm@box=\box59
\c@Hy@AnnotLevel=\count325
\HyField@AnnotCount=\count326
\Fld@listcount=\count327
\c@bookmark@seq@number=\count328
 (E:\MiKTeX\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)
 (E:\MiKTeX\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 284.
)
\Hy@SectionHShift=\skip76
) (E:\MiKTeX\tex/xelatex/xecjk\xeCJKfntef.sty
Package: xeCJKfntef 2022/08/05 v3.9.1 xeCJK font effect
 (E:\MiKTeX\tex/latex/ulem\ulem.sty
\UL@box=\box60
\UL@hyphenbox=\box61
\UL@skip=\skip77
\UL@hook=\toks22
\UL@height=\dimen197
\UL@pe=\count329
\UL@pixel=\dimen198
\ULC@box=\box62
Package: ulem 2019/11/18
\ULdepth=\dimen199
)
\l__xeCJK_space_skip=\skip78
\c__xeCJK_ulem-begin_node_dim=\dimen256
\l__xeCJK_hidden_box=\box63
\l__xeCJK_fntef_box=\box64
\l__xeCJK_under_symbol_box=\box65
\c__xeCJK_filll_skip=\skip79
) (E:\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (E:\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (E:\MiKTeX\tex/latex/graphics-def\xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
) (E:\MiKTeX\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
\c@resumebookmark=\count330
) (E:\MiKTeX\tex/latex/setspace\setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
) (main.aux)
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 17.
LaTeX Font Info:    ... okay on input line 17.
\symlegacymaths=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 17.
LaTeX Font Info:    Redeclaring math accent \acute on input line 17.
LaTeX Font Info:    Redeclaring math accent \grave on input line 17.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 17.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 17.
LaTeX Font Info:    Redeclaring math accent \bar on input line 17.
LaTeX Font Info:    Redeclaring math accent \breve on input line 17.
LaTeX Font Info:    Redeclaring math accent \check on input line 17.
LaTeX Font Info:    Redeclaring math accent \hat on input line 17.
LaTeX Font Info:    Redeclaring math accent \dot on input line 17.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 17.
LaTeX Font Info:    Redeclaring math symbol \colon on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 17.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 17.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 17.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 17.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 17.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 17.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 17.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 17.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 17.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 17.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 17.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(28.45274pt, 540.60239pt, 28.45274pt)
* v-part:(T,H,B)=(28.45274pt, 802.36774pt, 14.22636pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=540.60239pt
* \textheight=802.36774pt
* \oddsidemargin=-43.81725pt
* \evensidemargin=-43.81725pt
* \topmargin=-80.81725pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 17.
(main.out) (main.out)
\@outlinefile=\write3
\openout3 = `main.out'.



Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `\ResumeUrl' on input line 58.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `\heiti' on input line 58.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `\ResumeUrl' on input line 94.


Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                removing `\heiti' on input line 94.



[1

] (main.aux)
 ***********
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2022/08/05>
 ***********
Package rerunfilecheck Info: File `main.out' has not changed.
(rerunfilecheck)             Checksum: A6C258866F879A8B25A58D9605ED8CF3;884.
 ) 
Here is how much of TeX's memory you used:
 16104 strings out of 403265
 356672 string characters out of 5468475
 827979 words of memory out of 5000000
 44362 multiletter control sequences out of 15000+600000
 629189 words of font info for 76 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 107i,5n,125p,1248b,469s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on main.xdv (1 page, 38032 bytes).
