This is XeTeX, Version 3.141592653-2.6-0.999997 (MiKTeX 25.4) (preloaded format=xelatex 2025.7.27)  7 AUG 2025 00:15
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**c:/Users/<USER>/Desktop/resume/resume/main.tex
(c:/Users/<USER>/Desktop/resume/resume/main.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-19>
(resume.cls (E:\MiKTeX\tex/latex/l3kernel\expl3.sty
Package: expl3 2025-07-19 L3 programming layer (loader) 
 (E:\MiKTeX\tex/latex/l3backend\l3backend-xetex.def
File: l3backend-xetex.def 2025-06-09 L3 backend support: XeTeX
\g__graphics_track_int=\count271
\g__pdfannot_backend_int=\count272
\g__pdfannot_backend_link_int=\count273
)) (E:\MiKTeX\tex/latex/l3packages/l3keys2e\l3keys2e.sty
Package: l3keys2e 2024-08-16 LaTeX2e option processing using LaTeX3 keys
)
Document Class: resume 2022-12-26 v0.1.0 Another Resume Class by Feng Kaiyu
(E:\MiKTeX\tex/latex/ctex\ctexart.cls (E:\MiKTeX\tex/latex/ctex/config\ctexbackend.cfg
File: ctexbackend.cfg 2022/07/14 v2.5.10 Backend configuration file (CTEX)
)
Document Class: ctexart 2022/07/14 v2.5.10 Chinese adapter for class article (CTEX)
(E:\MiKTeX\tex/latex/ctex\ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (E:\MiKTeX\tex/latex/ctex\ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
) (E:\MiKTeX\tex/latex/base\fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (E:\MiKTeX\tex/latex/base\ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
LaTeX Encoding Info:    Redeclaring text command \capitalcedilla (encoding TS1) on input line 49.
LaTeX Encoding Info:    Redeclaring text command \capitalogonek (encoding TS1) on input line 52.
LaTeX Encoding Info:    Redeclaring text command \capitalgrave (encoding TS1) on input line 55.
LaTeX Encoding Info:    Redeclaring text command \capitalacute (encoding TS1) on input line 56.
LaTeX Encoding Info:    Redeclaring text command \capitalcircumflex (encoding TS1) on input line 57.
LaTeX Encoding Info:    Redeclaring text command \capitaltilde (encoding TS1) on input line 58.
LaTeX Encoding Info:    Redeclaring text command \capitaldieresis (encoding TS1) on input line 59.
LaTeX Encoding Info:    Redeclaring text command \capitalhungarumlaut (encoding TS1) on input line 60.
LaTeX Encoding Info:    Redeclaring text command \capitalring (encoding TS1) on input line 61.
LaTeX Encoding Info:    Redeclaring text command \capitalcaron (encoding TS1) on input line 62.
LaTeX Encoding Info:    Redeclaring text command \capitalbreve (encoding TS1) on input line 63.
LaTeX Encoding Info:    Redeclaring text command \capitalmacron (encoding TS1) on input line 64.
LaTeX Encoding Info:    Redeclaring text command \capitaldotaccent (encoding TS1) on input line 65.
LaTeX Encoding Info:    Redeclaring text command \t (encoding TS1) on input line 66.
LaTeX Encoding Info:    Redeclaring text command \capitaltie (encoding TS1) on input line 67.
LaTeX Encoding Info:    Redeclaring text command \newtie (encoding TS1) on input line 68.
LaTeX Encoding Info:    Redeclaring text command \capitalnewtie (encoding TS1) on input line 69.
LaTeX Encoding Info:    Redeclaring text symbol \textcapitalcompwordmark (encoding TS1) on input line 70.
LaTeX Encoding Info:    Redeclaring text symbol \textascendercompwordmark (encoding TS1) on input line 71.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightbase (encoding TS1) on input line 72.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightdblbase (encoding TS1) on input line 73.
LaTeX Encoding Info:    Redeclaring text symbol \texttwelveudash (encoding TS1) on input line 74.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequartersemdash (encoding TS1) on input line 75.
LaTeX Encoding Info:    Redeclaring text symbol \textleftarrow (encoding TS1) on input line 76.
LaTeX Encoding Info:    Redeclaring text symbol \textrightarrow (encoding TS1) on input line 77.
LaTeX Encoding Info:    Redeclaring text symbol \textblank (encoding TS1) on input line 78.
LaTeX Encoding Info:    Redeclaring text symbol \textdollar (encoding TS1) on input line 79.
LaTeX Encoding Info:    Redeclaring text symbol \textquotesingle (encoding TS1) on input line 80.
LaTeX Encoding Info:    Redeclaring text command \textasteriskcentered (encoding TS1) on input line 81.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphen (encoding TS1) on input line 92.
LaTeX Encoding Info:    Redeclaring text symbol \textfractionsolidus (encoding TS1) on input line 93.
LaTeX Encoding Info:    Redeclaring text symbol \textzerooldstyle (encoding TS1) on input line 94.
LaTeX Encoding Info:    Redeclaring text symbol \textoneoldstyle (encoding TS1) on input line 95.
LaTeX Encoding Info:    Redeclaring text symbol \texttwooldstyle (encoding TS1) on input line 96.
LaTeX Encoding Info:    Redeclaring text symbol \textthreeoldstyle (encoding TS1) on input line 97.
LaTeX Encoding Info:    Redeclaring text symbol \textfouroldstyle (encoding TS1) on input line 98.
LaTeX Encoding Info:    Redeclaring text symbol \textfiveoldstyle (encoding TS1) on input line 99.
LaTeX Encoding Info:    Redeclaring text symbol \textsixoldstyle (encoding TS1) on input line 100.
LaTeX Encoding Info:    Redeclaring text symbol \textsevenoldstyle (encoding TS1) on input line 101.
LaTeX Encoding Info:    Redeclaring text symbol \texteightoldstyle (encoding TS1) on input line 102.
LaTeX Encoding Info:    Redeclaring text symbol \textnineoldstyle (encoding TS1) on input line 103.
LaTeX Encoding Info:    Redeclaring text symbol \textlangle (encoding TS1) on input line 104.
LaTeX Encoding Info:    Redeclaring text symbol \textminus (encoding TS1) on input line 105.
LaTeX Encoding Info:    Redeclaring text symbol \textrangle (encoding TS1) on input line 106.
LaTeX Encoding Info:    Redeclaring text symbol \textmho (encoding TS1) on input line 107.
LaTeX Encoding Info:    Redeclaring text symbol \textbigcircle (encoding TS1) on input line 108.
LaTeX Encoding Info:    Redeclaring text command \textcircled (encoding TS1) on input line 109.
LaTeX Encoding Info:    Redeclaring text symbol \textohm (encoding TS1) on input line 115.
LaTeX Encoding Info:    Redeclaring text symbol \textlbrackdbl (encoding TS1) on input line 116.
LaTeX Encoding Info:    Redeclaring text symbol \textrbrackdbl (encoding TS1) on input line 117.
LaTeX Encoding Info:    Redeclaring text symbol \textuparrow (encoding TS1) on input line 118.
LaTeX Encoding Info:    Redeclaring text symbol \textdownarrow (encoding TS1) on input line 119.
LaTeX Encoding Info:    Redeclaring text symbol \textasciigrave (encoding TS1) on input line 120.
LaTeX Encoding Info:    Redeclaring text symbol \textborn (encoding TS1) on input line 121.
LaTeX Encoding Info:    Redeclaring text symbol \textdivorced (encoding TS1) on input line 122.
LaTeX Encoding Info:    Redeclaring text symbol \textdied (encoding TS1) on input line 123.
LaTeX Encoding Info:    Redeclaring text symbol \textleaf (encoding TS1) on input line 124.
LaTeX Encoding Info:    Redeclaring text symbol \textmarried (encoding TS1) on input line 125.
LaTeX Encoding Info:    Redeclaring text symbol \textmusicalnote (encoding TS1) on input line 126.
LaTeX Encoding Info:    Redeclaring text symbol \texttildelow (encoding TS1) on input line 127.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphenchar (encoding TS1) on input line 128.
LaTeX Encoding Info:    Redeclaring text symbol \textasciibreve (encoding TS1) on input line 129.
LaTeX Encoding Info:    Redeclaring text symbol \textasciicaron (encoding TS1) on input line 130.
LaTeX Encoding Info:    Redeclaring text symbol \textacutedbl (encoding TS1) on input line 131.
LaTeX Encoding Info:    Redeclaring text symbol \textgravedbl (encoding TS1) on input line 132.
LaTeX Encoding Info:    Redeclaring text symbol \textdagger (encoding TS1) on input line 133.
LaTeX Encoding Info:    Redeclaring text symbol \textdaggerdbl (encoding TS1) on input line 134.
LaTeX Encoding Info:    Redeclaring text symbol \textbardbl (encoding TS1) on input line 135.
LaTeX Encoding Info:    Redeclaring text symbol \textperthousand (encoding TS1) on input line 136.
LaTeX Encoding Info:    Redeclaring text symbol \textbullet (encoding TS1) on input line 137.
LaTeX Encoding Info:    Redeclaring text symbol \textcelsius (encoding TS1) on input line 138.
LaTeX Encoding Info:    Redeclaring text symbol \textdollaroldstyle (encoding TS1) on input line 139.
LaTeX Encoding Info:    Redeclaring text symbol \textcentoldstyle (encoding TS1) on input line 140.
LaTeX Encoding Info:    Redeclaring text symbol \textflorin (encoding TS1) on input line 141.
LaTeX Encoding Info:    Redeclaring text symbol \textcolonmonetary (encoding TS1) on input line 142.
LaTeX Encoding Info:    Redeclaring text symbol \textwon (encoding TS1) on input line 143.
LaTeX Encoding Info:    Redeclaring text symbol \textnaira (encoding TS1) on input line 144.
LaTeX Encoding Info:    Redeclaring text symbol \textguarani (encoding TS1) on input line 145.
LaTeX Encoding Info:    Redeclaring text symbol \textpeso (encoding TS1) on input line 146.
LaTeX Encoding Info:    Redeclaring text symbol \textlira (encoding TS1) on input line 147.
LaTeX Encoding Info:    Redeclaring text symbol \textrecipe (encoding TS1) on input line 148.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobang (encoding TS1) on input line 149.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobangdown (encoding TS1) on input line 150.
LaTeX Encoding Info:    Redeclaring text symbol \textdong (encoding TS1) on input line 151.
LaTeX Encoding Info:    Redeclaring text symbol \texttrademark (encoding TS1) on input line 152.
LaTeX Encoding Info:    Redeclaring text symbol \textpertenthousand (encoding TS1) on input line 153.
LaTeX Encoding Info:    Redeclaring text symbol \textpilcrow (encoding TS1) on input line 154.
LaTeX Encoding Info:    Redeclaring text symbol \textbaht (encoding TS1) on input line 155.
LaTeX Encoding Info:    Redeclaring text symbol \textnumero (encoding TS1) on input line 156.
LaTeX Encoding Info:    Redeclaring text symbol \textdiscount (encoding TS1) on input line 157.
LaTeX Encoding Info:    Redeclaring text symbol \textestimated (encoding TS1) on input line 158.
LaTeX Encoding Info:    Redeclaring text symbol \textopenbullet (encoding TS1) on input line 159.
LaTeX Encoding Info:    Redeclaring text symbol \textservicemark (encoding TS1) on input line 160.
LaTeX Encoding Info:    Redeclaring text symbol \textlquill (encoding TS1) on input line 161.
LaTeX Encoding Info:    Redeclaring text symbol \textrquill (encoding TS1) on input line 162.
LaTeX Encoding Info:    Redeclaring text symbol \textcent (encoding TS1) on input line 163.
LaTeX Encoding Info:    Redeclaring text symbol \textsterling (encoding TS1) on input line 164.
LaTeX Encoding Info:    Redeclaring text symbol \textcurrency (encoding TS1) on input line 165.
LaTeX Encoding Info:    Redeclaring text symbol \textyen (encoding TS1) on input line 166.
LaTeX Encoding Info:    Redeclaring text symbol \textbrokenbar (encoding TS1) on input line 167.
LaTeX Encoding Info:    Redeclaring text symbol \textsection (encoding TS1) on input line 168.
LaTeX Encoding Info:    Redeclaring text symbol \textasciidieresis (encoding TS1) on input line 169.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyright (encoding TS1) on input line 170.
LaTeX Encoding Info:    Redeclaring text symbol \textordfeminine (encoding TS1) on input line 171.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyleft (encoding TS1) on input line 172.
LaTeX Encoding Info:    Redeclaring text symbol \textlnot (encoding TS1) on input line 173.
LaTeX Encoding Info:    Redeclaring text symbol \textcircledP (encoding TS1) on input line 174.
LaTeX Encoding Info:    Redeclaring text symbol \textregistered (encoding TS1) on input line 175.
LaTeX Encoding Info:    Redeclaring text symbol \textasciimacron (encoding TS1) on input line 176.
LaTeX Encoding Info:    Redeclaring text symbol \textdegree (encoding TS1)